<template>
  <a-modal
    :width="800"
    v-model:open="state.modalVisible"
    :zIndex="1009"
    :maskClosable="false"
    :title="state.modalTitle"
    :confirmLoading="state.modalConfirmLoading"
    @cancel="handleOk"
  >
    <div class="data-import_body">
      <!-- 上传页面 -->
      <a-spin :tip="'数据导入中...'" :spinning="state.uploadSpinning">
        <template v-if="state.uploadStatus === 1">
          <transition name="fade-in-linear">
            <div class="data-import_base__info">
              <h5 class="dibi_title">一、下载模板，填写导入数据信息</h5>
              <p class="dibi_hint">
                请按照数据模板的格式填写数据，模板中的表头不可更改，表头不可删除
              </p>
              <div>
                <template v-if="importObj.staticFile">
                  <!-- 使用a标签直接下载静态文件 -->
                  <a
                    style="margin-right: 10px"
                    v-for="(item, idx) in importObj.exprots"
                    :key="idx"
                    :href="`${item.exprotUrl}?t=${new Date().getTime()}`"
                    :download="item.downloadText"
                  >
                    <a-button class="btn">
                      <template #icon>
                        <DownloadOutlined class="icon-color" />
                      </template>
                      下载模板({{ item.downloadText }})
                    </a-button>
                  </a>
                </template>
                <template v-else>
                  <!-- 通过接口下载动态文件 -->
                  <div v-for="(item, idx) in importObj.exprots" :key="idx">
                    <a-button class="btn" @click="downloadFile(item)">
                      <template #icon>
                        <DownloadOutlined class="icon-color" />
                      </template>
                      下载模板({{ item.downloadText }})
                    </a-button>
                  </div>
                </template>
              </div>
            </div>
          </transition>
          <transition name="fade-in-linear">
            <div class="data-export_base__info">
              <h5 class="dibi_title">二、选择您要上传的文件</h5>
              <p class="dibi_hint">
                只能上传""XLS,XLSX"的文件，且文件大小不超过10M
              </p>
              <a-upload
                :accept="importObj.accept"
                :multiple="false"
                :before-upload="beforeUpload"
                :auto-upload="false"
                :file-list="fileLists"
                :limit="1"
                @change="handleChange"
              >
                <a-button class="btn">
                  <template #icon>
                    <UploadOutlined class="icon-color" />
                  </template>
                  上传文件
                </a-button>
              </a-upload>
            </div>
          </transition>
          <div class="tip">
            <p>
              <exclamation-circle-filled
                style="margin-right: 10px; color: #faad14"
              />特别提示：导入过程中如发现相同数据，则新数据将覆盖掉现有数据
            </p>
          </div>
        </template>

        <!-- 导入成功 -->
        <template v-if="state.uploadStatus === 2">
          <transition name="el-fade-in-linear">
            <div class="data-import_success">
              <template v-if="importData.errorCount">
                <close-circle-outlined style="color: red; font-size: 40px" />
                <h3 class="dis_title" style="margin: 0">数据导入失败</h3>
                <p>
                  未导入
                  <span class="dis_hint__danger">
                    {{ importData.errorCount }} </span
                  >条数据
                </p>
                <div
                  class="abnormal_data__list"
                  style="text-align: left; margin-top: 10px"
                >
                  <div class="dia-adl_title">异常提示：</div>
                  <ul class="dia-adl_list">
                    <li v-for="(item, idx) in 100" :key="idx">DSDsdfas</li>
                  </ul>
                </div>
              </template>
              <template v-else>
                <div>
                  <check-circle-filled
                    style="
                    color: var(--primary-color);
                    font-size: 40px;
                  "
                />
                </div>
                <h3 class="dis_title">数据导入完成</h3>
                <p class="dis_hint">
                  您已成功导入
                  <span class="dis_hint__success">
                    {{ importData.successCount }}
                  </span>
                  条数据，未导入
                  <span class="dis_hint__danger">
                    {{ importData.errorCount }}
                  </span>
                  条数据
                </p>
              </template>
            </div>
          </transition>
        </template>

        <!-- 导入异常数据 -->
        <template v-if="state.uploadStatus === 3">
          <transition name="el-fade-in-linear">
            <div class="data-import_abnormal">
              <a-progress
                class="mg20"
                v-if="state.percentLen && state.percentLen < 100"
                :percent="state.percentLen"
                status="active"
              />
              <div>
                <div class="error-download" v-if="importData.errorMsg.length">
                  <a-button type="link" @click="getExportErrorData"
                    >下载异常数据</a-button
                  >
                </div>

                <div class="abnormal_data__num">
                  <div class="abnormal_data__numtext">
                    正常数据条数:
                    <span class="abnormal_data__successnum">
                      {{ importData.successCount }}
                    </span>
                    条
                  </div>
                  <div class="abnormal_data__numtext">
                    异常数据条数:
                    <span class="abnormal_data__errornum"
                      >{{ importData.errorCount }}
                    </span>
                    条
                  </div>
                </div>
                <div class="abnormal_data__list">
                  <div class="dia-adl_title">异常提示：</div>
                  <ul class="dia-adl_list">
                    <li v-for="(item, idx) in importData.errorMsg" :key="idx">
                      {{ item }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </transition>
        </template>
      </a-spin>
    </div>
    <template #footer>
      <a-button @click="handleOk">关闭</a-button>
      <a-button type="primary" v-if="state.uploadStatus === 3" @click="reUpload"
        >重新上传</a-button
      >
    </template>
  </a-modal>
</template>

<script setup>
// const route = useRoute();
const emit = defineEmits(['handleOk']);
// 这里是导入的参数 需要对外暴露
const importObj = reactive({
  staticFile: true, // 下载的模板内容是不是静态的文件
  cbkImport: false,
  isSampling: false,
  importUrl: '/manage/collect/job/import', // 导入的接口url
  importParams: {}, // 导入携带的参数
  // 模板个数
  exprots: [
    {
      downloadText: '模版', // 模板的名称
      exprotUrl: 'https://xxx', // 导出
    },
  ],
  accept:
    '.doc,.docx,.xlsx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
});

const fileList = shallowRef([]);
const fileLists = shallowRef([]);

const importData = shallowRef({
  successCount: 0,
  errorCount: 0,
  errorMsg: [],
  id: '',
});

const state = reactive({
  modalVisible: false,
  successId: null,
  uploadStatus: 1, // 1 上传页面 2 导入成功页面 3 导入异常页面
  percentLen: 0,
  importID: '',
  tip: '数据导入中...',
  modalTitle: '数据导入',
  time: null,
  spinning: false,
  uploadSpinning: false,
  modalConfirmLoading: false,
});

/* 重新导入 */
const reUpload = () => {
  state.uploadStatus = 1;
  fileLists.value = [];
};

// 获取上传进度
const getUploadprogres = id => {
  http
    .get('/manage/import/getSchedule', { id: id })
    .then(res => {
      const { successCount, totalRow, schedule } = res.data;
      if (schedule !== 100 || !res.data.hasOwnProperty('successCount')) {
        // state.percentLen = schedule
        state.time = setTimeout(() => {
          getUploadprogres(id);
        }, 1000);
      } else {
        state.uploadSpinning = false;
        // state.percentLen = schedule
        importData.value = res.data;
        clearInterval(state.time);
        importData.value = { ...importData.value, ...res.data };
        if (successCount === totalRow) {
          state.uploadStatus = 2;
        } else {
          state.uploadStatus = 3;
        }
      }
    })
    .catch(() => {
      fileLists.value = [];
      state.uploadSpinning = false;
    })
    .finally(() => {
      // state.uploadSpinning = false
    });
};

/* 上传文件 */
const handleChange = ({ fileList }) => {
  const resFileList = fileList.slice(-1);
  fileLists.value = resFileList;
  state.uploadSpinning = true;
  const params = {};
  fileLists.value.forEach(fileItem => {
    params.file = fileItem.originFileObj;
  });
  http
    .form(importObj.importUrl, {
      ...params,
      ...importObj.importParams,
    })
    .then(res => {
      const { data } = res;
      state.successId = data;
      getUploadprogres(data);
    })
    .catch(() => {
      fileLists.value = [];
      state.uploadSpinning = false;
    });
};

const beforeUpload = file => {
  fileList.value = [...fileList.value, file];
  return false;
};

// 下载导入错误数据
const getExportErrorData = () => {
  http.download(
    '/manage/import/exportErrorData',
    { id: state.successId },
    '异常数据下载'
  );
};

// 关闭
const handleOk = () => {
  clearInterval(state.time);
  if (state.uploadStatus !== 1) {
    importObj.cbkImport = !importObj.cbkImport;
  }
  state.uploadStatus = 1;
  fileLists.value = [];
  state.uploadSpinning = false;
  state.modalVisible = false;
  emit('handleOk'); //快点刷新一下列表
};

// 通过接口下载动态文件
const downloadFile = data => {
  console.log('data', data);
  http.download(data.exprotUrl, data.params, data.fileName);
};

// 打开弹窗合并参数
const show = (data = {}) => {
  state.modalVisible = true;
  Object.assign(importObj, data);
};

defineExpose({ show });
</script>

<style scoped lang="less">
.data-import_success,
.data-export_base__info,
.data-import_base__info {
  &:hover {
    border-color: var(--primary-color);
  }

  margin-bottom: 20px;
  // background: @gray-background;
  border: 1px dashed @border-color-base;
  padding: 32px;

  .dibi_hint {
    padding: 10px 0;
    color: @suggestive-color;
  }

  .icon-color {
    color: var(--primary-color);
  }
}
.data-import_body {
  padding: 24px;
}
.dibi_title {
  font-weight: bold;
  font-size: 14px;
}

.error-download {
  text-align: right;

  .ant-btn-link {
    color: var(--primary-color);
    padding: 0;
  }
}

// 异常数据
.data-import_abnormal {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 200px;

  .mg20 {
    margin-top: 80px;

    :deep(.ant-progress-outer) {
      margin-right: calc(-2em - 38px) !important;
      padding-right: calc(2em + 38px) !important;
    }
  }

  .abnormal_hint {
    height: 48px;
    line-height: 48px;
    background: #fdf6ec;
    border: 1px solid #faecd8;
    color: #e6a23c;
    border-radius: 4px;
    padding: 0 20px;

    .dis_icon {
      padding-right: 10px;
      font-size: 16px;
    }
  }

  .abnormal_data__num {
    height: 98px;
    margin: 15px 0;
    border: 1px solid rgba(0, 0, 0, 0.15);
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 2px;
    color: #303133;
    font-weight: bold;

    .abnormal_data__numtext {
      &:first-child {
        margin-bottom: 10px;
      }
    }

    .abnormal_data__successnum {
      color: #2c8aff;
      padding-right: 3px;
    }

    .abnormal_data__errornum {
      color: #f56c6c;
      padding-right: 3px;
    }
  }
}

// 导入成功
.data-import_success {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  text-align: center;

  .dis_icon {
    font-size: 42px;
    color: #2c8aff;
  }

  .dis_title {
    font-size: 24px;
    color: #303133;
    margin: 12px 0;
  }

  .dis_hint {
    font-size: 14px;
    color: #303133;
  }

  .dis_hint__danger {
    color: #f56c6c;
    padding-right: 3px;
  }

  .dis_hint__success {
    color: #2c8aff;
    padding-right: 3px;
  }
}

.abnormal_data__list {
  flex: 1;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  overflow-y: auto;

  .dia-adl_list,
  .dia-adl_title {
    padding: 0 20px;
  }
}

.dia-adl_title {
  color: #303133;
  font-size: 14px;
  font-weight: bold;
  margin: 10px 0;
}

.dia-adl_list {
  overflow: hidden auto;
  max-height: 300px;

  li {
    padding: 3px 0;
    color: #909399;
  }
}

// 进度条
.data-import_loading {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  text-align: center;

  .dil_progress {
    margin: 12px 0;
  }
}

.tip {
  position: relative;
}
.btn {
  background: #e8fbf5;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}
</style>
