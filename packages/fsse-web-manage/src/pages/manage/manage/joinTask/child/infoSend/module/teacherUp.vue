<template>
  <searchForm
    v-model:formState="query"
    :formList="formList"
    @submit="searchBtn"
    @reset="resetBtn"
  />
  <div class="feat-btn-group">
    <a-button @click="importBtn"> 导入 </a-button>
    <a-button @click="exportBtn"> 导出 </a-button>
    <a-button
      danger
      :disabled="!state.selectedRowKeys.length"
      @click="deleteItems"
    >
      删除
    </a-button>
  </div>
  <ETable
    :minH="400"
    hash="teacherUpTable"
    :loading="page.loading"
    :columns="columns"
    :dataSource="computedList"
    :total="page.total"
    @paginationChange="paginationChange"
    :current="query.pageNo"
    :row-selection="{
      selectedRowKeys: state.selectedRowKeys,
      onChange: onSelectChange,
    }"
  >
    <template #isLeader="{ record }">
      {{ record.isLeader ? '是' : '否' }}
    </template>
    <template #isManager="{ record }">
      {{ record.isManager ? '是' : '否' }}
    </template>
    <template #isPrincipal="{ record }">
      {{ record.isPrincipal ? '是' : '否' }}
    </template>
  </ETable>

  <div class="footBox">
    <a-button
      ghost
      style="width: 244px"
      size="large"
      :block="true"
      type="primary"
      @click="backStep"
      >上一步</a-button
    >
    <a-button
      style="width: 244px"
      size="large"
      :block="true"
      type="primary"
      @click="nextStep"
      >下一步</a-button
    >
  </div>
  <Import ref="ImportRef" @handleOk="resetBtn"></Import>
</template>

<script setup name="TeacherUp">
import { createVNode } from 'vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
const emit = defineEmits(['changeActivateItem']);
const route = useRoute();
import { Modal } from 'ant-design-vue';

const mainStore = useStore();
// let orgId = mainStore?.userInfo?.orgId || '';
// let areaId = mainStore?.userInfo?.fsseOrg?.areaId || '';
let { query, page, getList, reset, paginationChange, updateByDelete } = useList(
  '/manage/collect/job/teacher/page',
  {
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  }
);
getList();
const state = ref({
  selectedRowKeys: [],
});

const ImportRef = ref(null);

const formList = [
  {
    type: 'input',
    value: 'name',
    label: '教师姓名',
  },
];

// const columns = ref([
//   { title: '教师姓名', dataIndex: 'name', key: 'name' },
//   { title: '手机号', dataIndex: 'phone', key: 'phone' },
//   { title: '出生年月', dataIndex: 'birthday', key: 'birthday' },
//   { title: '上学期任教科目', dataIndex: 'subjectName', key: 'subjectName' },
//   { title: '所属学段', dataIndex: 'section', key: 'section' },
//   { title: '上学期是否为班主任', dataIndex: 'isLeader', key: 'isLeader' },
//   { title: '是否为管理干部', dataIndex: 'isManager', key: 'isManager' },
//   { title: '是否为校长', dataIndex: 'isPrincipal', key: 'isPrincipal' },
// ]);

const columns = ref([
  { title: '教师姓名', dataIndex: 'name', key: 'name' },
  { title: '手机号', dataIndex: 'phone', key: 'phone' },
  { title: '出生日期', dataIndex: 'birthday', key: 'birthday' },
  { title: '任教年级', dataIndex: 'gradeName', key: 'gradeName' },
  { title: '任教科目', dataIndex: 'subjectName', key: 'subjectName' },
  { title: '所属学段', dataIndex: 'section', key: 'section' },
  { title: '是否为班主任', dataIndex: 'isLeader', key: 'isLeader' },
  { title: '是否为校长', dataIndex: 'isPrincipal', key: 'isPrincipal' },
  { title: '备注', dataIndex: 'remark', key: 'remark' },
]);

const backStep = () => {
  console.log('上一步');
  emit('changeActivateItem', 'StudentUp');
};

const nextStep = () => {
  emit('changeActivateItem', 'ClassroomUp');
};

const onSelectChange = (selectedRowKeys) => {
  state.value.selectedRowKeys = selectedRowKeys;
};

// 获取指标的动态表头
const getColumns = async () => {
  try {
    const res = await http.post('/manage/collect/job/listCollectJobIndicator', {
      collectJobId: route.query.collectJobId,
      indicatorType: 'teacher',
    });

    // 过滤出 checked 字段为 true的
    const checkedArr = res.data.filter((item) => item.checked === true);
    const columnsArr = checkedArr.map((item) => {
      return {
        title: item.name,
        dataIndex: item.id,
        key: item.id,
      };
    });

    console.log(res);

    columns.value = [...columns.value, ...columnsArr];
  } catch (e) {
    console.warn(e);
  }
};

// 查询
const searchBtn = () => {
  getList({
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  });
};

// 重置
const resetBtn = () => {
  reset({
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  });
};

const deleteItems = () => {
  Modal.confirm({
    title: '删除',
    icon: createVNode(ExclamationCircleFilled),
    content: `是否确认删除？`,
    okText: '确 定',
    cancelText: '取 消',
    onCancel() {},
    async onOk() {
      await http.post(`/manage/collect/job/teacher/delete`, {
        ids: state.value.selectedRowKeys,
      });
      YMessage.success('操作成功');
      state.value.selectedRowKeys = [];
      updateByDelete(state.value.selectedRowKeys.length);
    },
  });
};

const importBtn = () => {
  ImportRef.value.show({
    staticFile: false, // 需要动态下载文件
    importUrl: '/manage/collect/job/import', // 导入链接
    exprotUrl: '/manage/collect/job/teacher/export',
    importParams: {
      collectJobId: route.query.collectJobId,
      stepId: 8,
    }, // 导入参数
    exprots: [
      {
        fileName: '教师信息上报模板', // 文件名
        downloadText: '教师信息上报模板', // 按钮显示名称
        exprotUrl: '/manage/collect/job/teacher/export', // 导出
        params: {
          collectJobId: route.query.collectJobId,
          template: true,
        },
      },
    ],
  });
};

// 导出
const exportBtn = () => {
  try {
    http.download(
      `/manage/collect/job/teacher/export`,
      {
        collectJobId: route.query.collectJobId,
        template: false,
        name: query.name,
        // areaId: areaId,
      },
      '教师信息上报'
    );
  } catch (e) {
    console.warn(e);
  }
};

const computedList = computed(() => {
  // 拼接表格数据
  let resultArr = page.list.reduce((result, item) => {
    if (item.indicators && item.indicators.length) {
      let newItem = { ...item };
      item.indicators.forEach((indicator) => {
        newItem[indicator.indicatorId] = indicator.indicatorRuleValue;
      });
      result.push(newItem);
    } else {
      result.push(item);
    }
    return result;
  }, []);

  return resultArr;
});

onMounted(() => {
  getColumns();
});
</script>

<style lang="less" scoped>
.feat-btn-group {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
}

.footBox {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 16px;
}
</style>
