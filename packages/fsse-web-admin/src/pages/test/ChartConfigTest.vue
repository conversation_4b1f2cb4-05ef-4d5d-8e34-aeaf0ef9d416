<template>
  <div class="chart-config-test">
    <h2>图表配置功能测试</h2>
    
    <a-card title="测试图表选择和配置">
      <a-button type="primary" @click="openModelModal">
        打开图表选择弹窗
      </a-button>
      
      <div v-if="selectedChart" class="selected-chart-info">
        <h3>已选择的图表信息：</h3>
        <p><strong>图表类型：</strong>{{ selectedChart.chart }}</p>
        <p><strong>语义代码：</strong>{{ selectedChart.semanticCode }}</p>
        <p><strong>指标代码：</strong>{{ selectedChart.indicatorCode }}</p>
        <p><strong>描述：</strong>{{ selectedChart.description }}</p>
        
        <div v-if="selectedChart.chartConfiguration">
          <h4>图表配置：</h4>
          <pre>{{ JSON.stringify(selectedChart.chartConfiguration, null, 2) }}</pre>
        </div>
      </div>
    </a-card>
    
    <!-- 图表选择弹窗 -->
    <Model ref="modelRef" @confirm="handleChartConfirm" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import Model from '../reportManage/editor/Model.vue';

// 状态管理
const selectedChart = ref(null);
const modelRef = ref(null);

// 模拟的记录数据
const mockRecord = {
  id: 315,
  code: 'METRIC_10080039',
  name: '学生文化理解素养具体表现',
  description: '文化理解素养各维度具体表现',
  typeId: 1,
  typeName: '学生指标',
  modelId: 42,
  datasourceId: 1,
  createBy: '超级管理员',
  createTime: '2024-08-30 15:32:00',
  versionCode: 'M202408-1008'
};

// 打开图表选择弹窗
const openModelModal = () => {
  if (modelRef.value) {
    // 直接跳转到带有templateId参数的URL
    const router = useRouter();
    router.push('/test/chart-config?id=123');

    // 延迟一下再打开弹窗，确保路由参数已更新
    setTimeout(() => {
      modelRef.value.showModal(mockRecord, {});
    }, 100);
  } else {
    message.error('弹窗组件未正确加载');
  }
};

// 处理图表确认选择
const handleChartConfirm = (chartData) => {
  selectedChart.value = chartData;
  message.success('图表配置已保存！');
  console.log('选择的图表数据:', chartData);
};
</script>

<style lang="less" scoped>
.chart-config-test {
  padding: 24px;
  
  h2 {
    margin-bottom: 24px;
    color: #262626;
  }
  
  .selected-chart-info {
    margin-top: 24px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 4px;
    
    h3, h4 {
      margin-bottom: 12px;
      color: #262626;
    }
    
    p {
      margin-bottom: 8px;
      color: #595959;
    }
    
    pre {
      background: #fff;
      padding: 12px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      overflow-x: auto;
      font-size: 12px;
    }
  }
}
</style>
