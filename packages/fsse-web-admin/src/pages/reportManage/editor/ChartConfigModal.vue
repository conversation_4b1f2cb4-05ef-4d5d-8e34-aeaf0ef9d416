<template>
  <a-modal
    v-model:open="state.open"
    width="800px"
    title="图表配置"
    :maskClosable="false"
    :destroyOnClose="true"
    :confirmLoading="state.confirmLoading"
    :closable="false"
    @ok="handleConfirm"
    @cancel="handleClear"
    :body-style="{ maxHeight: '600px', overflow: 'auto', padding: '24px' }"
  >
    <div class="chart-config-modal">
      <div class="config-header">
        <h4>{{ state.chartName }} - 配置参数</h4>
        <p class="config-tip">请在下方编辑器中修改配置参数，支持JSON对象格式</p>
      </div>

      <div class="config-editor">
        <CodeEditor
          ref="codeEditorRef"
          v-model:editCode="state.configCode"
          :height="'200px'"
          :options="{
            language: 'json',
            theme: 'vs',
          }"
        />
      </div>

      <div class="config-footer">
        <div class="config-example">
          <h5>配置说明：</h5>
          <ul>
            <li>groupBy: 'TEAMS' - 分组</li>
            <li>groupBy: 'NONE' - 不分组</li>
            <li>可以添加其他图表配置参数</li>
          </ul>
        </div>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button @click="handleClear" type="default" danger>
          清空配置
        </a-button>
        <!-- <a-button @click="handleCancel"> 取消 </a-button> -->
        <a-button
          @click="handleConfirm"
          type="primary"
          :loading="state.confirmLoading"
        >
          确定
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { reactive, ref, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import CodeEditor from '@/components/common/CodeEditor/index.vue';

// 定义事件
const emit = defineEmits(['confirm']);

// 引用
const codeEditorRef = ref(null);

// 状态管理
const state = reactive({
  open: false,
  confirmLoading: false,
  chartName: '',
  chartValue: '',
  configCode: '',
  templateId: '',
  metricId: '',
  metricCode: '',
});

// 显示弹窗
const showModal = (chartInfo, params = {}) => {
  state.open = true;
  state.chartName = chartInfo.name;
  state.chartValue = chartInfo.value;
  state.templateId = params.templateId || '';
  state.metricId = params.metricId || '';
  state.metricCode = params.metricCode || '';

  // 设置默认配置代码
  const defaultConfig = chartInfo.configuration || {
    chartOptions: {
      mappingHint: {
        groupBy: 'TEAMS',
      },
    },
  };

  // 格式化配置对象为可编辑的代码
  state.configCode = JSON.stringify(defaultConfig, null, 2);

  // 等待DOM更新后设置编辑器内容
  nextTick(() => {
    if (codeEditorRef.value) {
      // 确保编辑器内容正确设置
      setTimeout(() => {
        codeEditorRef.value.getCodeValue && codeEditorRef.value.getCodeValue();
      }, 100);
    }
  });
};

// 验证配置代码
const validateConfig = configStr => {
  try {
    const config = JSON.parse(configStr);

    console.log(config, '3213213123123123123');

    return { valid: true, config };
  } catch (error) {
    return { valid: false, error: error.message };
  }
};

// 确认配置
const handleConfirm = async () => {
  try {
    state.confirmLoading = true;

    // 获取编辑器内容
    const configCode = codeEditorRef.value?.getCodeValue() || state.configCode;

    if (!configCode.trim()) {
      message.error('配置内容不能为空');
      return;
    }

    // 验证配置格式
    const validation = validateConfig(configCode);
    if (!validation.valid) {
      message.error(`配置格式错误: ${validation.error}`);
      return;
    }

    console.log(configCode, '配置参数字符串');

    console.log(JSON.parse(configCode));

    // 调用API保存配置
    const params = {
      templateId: state.templateId,
      metricId: state.metricId,
      metricCode: state.metricCode,
      type: 'chart', // 配置类型
      param: JSON.stringify(JSON.parse(configCode)), // 配置参数字符串
    };

    const { data } = await http.post(
      '/admin/rpt/template/metric/param/create',
      params
    );

    message.success('图表配置保存成功');

    // 触发确认事件，传递配置信息
    emit('confirm', {
      chartValue: state.chartValue,
      configuration: validation.config,
      chartConfigurationId: data, // 返回的配置参数ID 最重要的
    });

    state.open = false;
  } catch (error) {
    console.error('保存图表配置失败:', error);
    message.error('保存配置失败，请重试');
  } finally {
    state.confirmLoading = false;
  }
};

// 清空配置
const handleClear = async () => {
  try {
    state.confirmLoading = true;

    // 清空编辑器内容
    state.configCode = '{}';
    if (codeEditorRef.value) {
      codeEditorRef.value.setCodeValue &&
        codeEditorRef.value.setCodeValue('{}');
    }

    message.success('配置已清空');

    // 触发确认事件，传递空配置
    emit('confirm', {
      chartValue: state.chartValue,
      configuration: {},
      chartConfigurationId: null, // 清空
    });

    state.open = false;
  } catch (error) {
    console.error('清空配置失败:', error);
    message.error('清空配置失败，请重试');
  } finally {
    state.confirmLoading = false;
  }
};

// 取消配置 - 也需要发送请求保存当前状态
const handleCancel = async () => {
  try {
    // 获取编辑器内容
    const configCode = codeEditorRef.value?.getCodeValue() || state.configCode;

    if (configCode.trim()) {
      // 验证配置格式
      const validation = validateConfig(configCode);
      if (validation.valid) {
        // 调用API保存配置（取消时也保存）
        const params = {
          templateId: state.templateId,
          metricId: state.metricId,
          metricCode: state.metricCode,
          type: 'chart',
          param: JSON.stringify(JSON.parse(configCode)), // 配置参数字符串
        };

        const { data } = await http.post(
          '/admin/rpt/template/metric/param/create',
          params
        );

        // 触发确认事件，传递配置信息
        emit('confirm', {
          chartValue: state.chartValue,
          configuration: validation.config,
          paramId: data, // 返回的参数ID
        });
      }
    }
  } catch (error) {
    console.error('取消时保存配置失败:', error);
    // 取消时的错误不显示给用户，静默处理
  } finally {
    state.open = false;
  }
};

// 暴露方法
defineExpose({
  showModal,
});
</script>

<style lang="less" scoped>
.chart-config-modal {
  .config-header {
    margin-bottom: 16px;

    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .config-tip {
      margin: 0;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .config-editor {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 16px;
  }

  .config-footer {
    .config-example {
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;

      h5 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }

      ul {
        margin: 0;
        padding-left: 16px;

        li {
          font-size: 12px;
          color: #595959;
          line-height: 20px;
        }
      }
    }
  }
}
</style>
