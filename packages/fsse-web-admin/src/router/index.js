import { createRouter, createWebHashHistory } from 'vue-router';
import getRoute from './getRoute';
import config from './config';
import { routeConfigs } from './routes.config.js'
const getAsRoute = async () => {
  const [routeArr] = await getRoute();
  const router = [
    {
      path: '',
      redirect: '/agency/region',
    },
    {
      component: () => import('@/components/layout/index.vue'),
      name: 'main',
      children: [
        ...routeArr,
        ...routeConfigs,
        {
          path: '/404',
          name: '404',
          component: () => import('../../../../components/common/404.vue'),
        },
        {
          path: '/no-auth',
          name: 'no-auth',
          component: () => import('../../../../components/common/NoAuth.vue'),
        },
        {
          path: '/redirect',
          name: 'redirect',
          component: () => import('../../../../components/common/redirect.vue'),
        },
      ],
    },
  ];
  return router;
};
const init = async () => {
  let routes = [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/components/login/index.vue'),
    },
    {
      path: '/:pathMatch(.*)',
      redirect: '/404',
    },
  ];
  if (window.location.hash.indexOf('login') == -1) {
    const arr = await getAsRoute();
    routes = [...routes, ...arr];
  }
  return Promise.resolve(routes);
};
const asRouter = async () => {
  return new Promise(resolve => {
    init().then(res => {
      const router = createRouter({
        history: createWebHashHistory(),
        scrollBehavior() {
          return { top: 0, left: 0 };
        },
        routes: res,
      });
      config(router);
      return resolve(router);
    });
  });
};

export default asRouter;
