export const routeConfigs = [
  // {
  //   path: '/agency',
  //   name: 'agency',
  //   meta: {
  //     title: '机构管理',
  //     icon: 'icon-jigouguanli',
  //   },
  //   children: [
  //     {
  //       path: '/region',
  //       name: 'region',
  //       meta: {
  //         title: '区域管理',
  //       },
  //       component: () => import('@/pages/agency/region/index.vue'),
  //     },
  //     {
  //       path: '/agency-manage',
  //       name: 'agency-manage',
  //       meta: {
  //         title: '机构管理',
  //       },
  //       component: () => import('@/pages/agency/manage/index.vue'),
  //     },
  //     {
  //       path: '/agency-info',
  //       name: 'agency-info',
  //       meta: {
  //         title: '机构详情',
  //         hidden: true,
  //       },
  //       component: () => import('@/pages/agency/info/index.vue'),
  //     },
  //   ],
  // },
  // {
  //   path: '/systemSettings',
  //   name: 'systemSettings',
  //   meta: {
  //     title: '系统',
  //     icon: 'icon-xitongshezhi',
  //   },
  //   children: [
  //     {
  //       path: '/systemSettings/role',
  //       name: 'sys_role',
  //       meta: {
  //         title: '角色管理',
  //       },
  //       component: () => import('@/pages/systemSettings/role/index.vue'),
  //     },
  //     {
  //       path: '/systemSettings/organization',
  //       name: 'sys_organization',
  //       meta: {
  //         title: '部门管理',
  //       },
  //       component: () =>
  //         import('@/pages/systemSettings/organization/index.vue'),
  //     },
  //     {
  //       path: '/systemSettings/user',
  //       name: 'sys_user',
  //       meta: {
  //         title: '用户管理',
  //       },
  //       component: () => import('@/pages/systemSettings/user/index.vue'),
  //     },
  //     {
  //       path: '/systemSettings/log',
  //       name: 'sys_log',
  //       meta: {
  //         title: '日志管理',
  //       },
  //       component: () => import('@/pages/systemSettings/log/index.vue'),
  //     },
  //     {
  //       path: '/systemSettings/menuConfig',
  //       name: 'sys_menuConfign',
  //       meta: {
  //         title: '菜单配置',
  //       },
  //       component: () => import('@/pages/systemSettings/menuConfig/index.vue'),
  //     },
  //     {
  //       path: '/systemSettings/dictionaryManaged',
  //       name: 'sys_dictionaryManaged',
  //       meta: {
  //         title: '字典管理',
  //       },
  //       component: () =>
  //         import(
  //           '@/pages/systemSettings/dictionaryManaged/index.vue'
  //         ),
  //     },
  //     {
  //       path: '/systemSettings/sysConfiguration',
  //       name: 'sys_configuration',
  //       meta: {
  //         title: '系统配置',
  //       },
  //       component: () => import('@/pages/systemSettings/sysConfiguration/index.vue'),
  //     },
  //   ],
  // },

  {
    path: '/test',
    name: 'test',
    meta: {
      title: '功能测试',
      icon: 'icon-setting',
    },
    children: [
      {
        path: '/test/chart-config',
        name: 'test_chart_config',
        meta: {
          title: '图表配置测试',
        },
        component: () => import('@/pages/test/ChartConfigTest.vue'),
      },
    ],
  },
  {
    path: '/copysystemSettings',
    name: 'copysystemSettings',
    meta: {
      title: '测试路径',
      icon: 'icon-xitongshezhi',
    },
    children: [
      {
        path: '/copysystemSettings/role',
        name: 'copysys_role',
        meta: {
          title: '角色管理',
        },
        component: () => import('@/pages/copysystemSettings/role/role.tsx'),
      },
      {
        path: '/copysystemSettings/organization',
        name: 'copysys_organization',
        meta: {
          title: '部门管理',
        },
        component: () =>
          import('@/pages/copysystemSettings/organization/organization.tsx'),
      },
      {
        path: '/copysystemSettings/user',
        name: 'copysys_user',
        meta: {
          title: '用户管理',
        },
        component: () => import('@/pages/copysystemSettings/user/user.tsx'),
      },
      {
        path: '/copysystemSettings/log',
        name: 'copysys_log',
        meta: {
          title: '日志管理',
        },
        component: () => import('@/pages/copysystemSettings/log/log.tsx'),
      },
      {
        path: '/copysystemSettings/menuConfig',
        name: 'copysys_menuConfign',
        meta: {
          title: '菜单配置',
        },
        component: () =>
          import('@/pages/copysystemSettings/menuConfig/menuConfig.tsx'),
      },
      {
        path: '/copysystemSettings/dictionaryManaged',
        name: 'copysys_dictionaryManaged',
        meta: {
          title: '字典管理',
        },
        component: () =>
          import(
            '@/pages/copysystemSettings/dictionaryManaged/dictionaryManaged.tsx'
          ),
      },
      {
        path: '/copysystemSettings/sysConfiguration',
        name: 'copysys_sysConfiguration',
        meta: {
          title: '系统配置',
        },
        component: () =>
          import(
            '@/pages/copysystemSettings/sysConfiguration/sysConfiguration.tsx'
          ),
      },
    ],
  },
];
